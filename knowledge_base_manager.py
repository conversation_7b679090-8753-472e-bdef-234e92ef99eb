#!/usr/bin/env python3
"""
LiveKit风水AI助手 - 知识库管理系统
基于LiveKit官方开发指南和最佳实践
"""

import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import asyncio
from functools import lru_cache

class KnowledgeBaseManager:
    """知识库管理器 - 符合LiveKit最佳实践"""
    
    def __init__(self, base_path: str = "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent"):
        self.base_path = Path(base_path)
        self.knowledge_file = self.base_path / "fengshui_knowledge.json"
        self.backup_dir = self.base_path / "knowledge_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 确保知识库文件存在
        if not self.knowledge_file.exists():
            self._create_default_knowledge_base()
    
    def _create_default_knowledge_base(self):
        """创建默认知识库结构"""
        default_kb = {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "room_advice": {},
            "direction_advice": {},
            "color_advice": {},
            "metadata": {
                "total_entries": 0,
                "categories": ["room_advice", "direction_advice", "color_advice"]
            }
        }
        
        with open(self.knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(default_kb, f, ensure_ascii=False, indent=2)
    
    def load_knowledge_base(self) -> Dict[str, Any]:
        """加载知识库"""
        try:
            with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 知识库加载失败: {e}")
            return {}
    
    def save_knowledge_base(self, knowledge: Dict[str, Any]) -> bool:
        """保存知识库"""
        try:
            # 创建备份
            self._create_backup()
            
            # 更新元数据
            knowledge["last_updated"] = datetime.now().isoformat()
            knowledge["metadata"] = self._calculate_metadata(knowledge)
            
            # 保存到文件
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(knowledge, f, ensure_ascii=False, indent=2)
            
            # 清除缓存以确保更新生效
            self._clear_cache()
            
            return True
        except Exception as e:
            print(f"❌ 知识库保存失败: {e}")
            return False
    
    def _create_backup(self):
        """创建知识库备份"""
        if self.knowledge_file.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"fengshui_knowledge_{timestamp}.json"
            shutil.copy2(self.knowledge_file, backup_file)
            
            # 只保留最近10个备份
            backups = sorted(self.backup_dir.glob("fengshui_knowledge_*.json"))
            if len(backups) > 10:
                for old_backup in backups[:-10]:
                    old_backup.unlink()
    
    def _calculate_metadata(self, knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """计算知识库元数据"""
        total_entries = 0
        categories = []
        
        for category in ["room_advice", "direction_advice", "color_advice"]:
            if category in knowledge and isinstance(knowledge[category], dict):
                count = len(knowledge[category])
                total_entries += count
                categories.append({"name": category, "count": count})
        
        return {
            "total_entries": total_entries,
            "categories": categories,
            "last_calculated": datetime.now().isoformat()
        }
    
    def _clear_cache(self):
        """清除LRU缓存以确保更新生效"""
        try:
            # 这里需要清除agent.py中的缓存
            # 由于缓存在另一个模块中，我们需要通过信号或重新加载来处理
            cache_clear_file = self.base_path / ".cache_clear_signal"
            cache_clear_file.touch()
        except Exception as e:
            print(f"⚠️ 缓存清除信号创建失败: {e}")
    
    # CRUD操作
    
    def add_room_advice(self, room_type: str, advice_data: Dict[str, Any]) -> bool:
        """添加房间建议"""
        knowledge = self.load_knowledge_base()
        if "room_advice" not in knowledge:
            knowledge["room_advice"] = {}
        
        knowledge["room_advice"][room_type] = advice_data
        return self.save_knowledge_base(knowledge)
    
    def update_room_advice(self, room_type: str, advice_data: Dict[str, Any]) -> bool:
        """更新房间建议"""
        knowledge = self.load_knowledge_base()
        if "room_advice" in knowledge and room_type in knowledge["room_advice"]:
            knowledge["room_advice"][room_type].update(advice_data)
            return self.save_knowledge_base(knowledge)
        return False
    
    def delete_room_advice(self, room_type: str) -> bool:
        """删除房间建议"""
        knowledge = self.load_knowledge_base()
        if "room_advice" in knowledge and room_type in knowledge["room_advice"]:
            del knowledge["room_advice"][room_type]
            return self.save_knowledge_base(knowledge)
        return False
    
    def add_direction_advice(self, direction: str, advice_data: Dict[str, Any]) -> bool:
        """添加方位建议"""
        knowledge = self.load_knowledge_base()
        if "direction_advice" not in knowledge:
            knowledge["direction_advice"] = {}
        
        knowledge["direction_advice"][direction] = advice_data
        return self.save_knowledge_base(knowledge)
    
    def add_color_advice(self, color: str, advice_data: Dict[str, Any]) -> bool:
        """添加颜色建议"""
        knowledge = self.load_knowledge_base()
        if "color_advice" not in knowledge:
            knowledge["color_advice"] = {}
        
        knowledge["color_advice"][color] = advice_data
        return self.save_knowledge_base(knowledge)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        knowledge = self.load_knowledge_base()
        
        stats = {
            "total_rooms": len(knowledge.get("room_advice", {})),
            "total_directions": len(knowledge.get("direction_advice", {})),
            "total_colors": len(knowledge.get("color_advice", {})),
            "last_updated": knowledge.get("last_updated", "未知"),
            "version": knowledge.get("version", "1.0"),
            "file_size_kb": round(self.knowledge_file.stat().st_size / 1024, 2) if self.knowledge_file.exists() else 0
        }
        
        stats["total_entries"] = stats["total_rooms"] + stats["total_directions"] + stats["total_colors"]
        
        return stats
    
    def export_knowledge_base(self, export_path: str) -> bool:
        """导出知识库"""
        try:
            knowledge = self.load_knowledge_base()
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(knowledge, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 知识库导出失败: {e}")
            return False
    
    def import_knowledge_base(self, import_path: str, merge: bool = False) -> bool:
        """导入知识库"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_knowledge = json.load(f)
            
            if merge:
                # 合并模式：将导入的数据与现有数据合并
                current_knowledge = self.load_knowledge_base()
                for category in ["room_advice", "direction_advice", "color_advice"]:
                    if category in imported_knowledge:
                        if category not in current_knowledge:
                            current_knowledge[category] = {}
                        current_knowledge[category].update(imported_knowledge[category])
                
                return self.save_knowledge_base(current_knowledge)
            else:
                # 替换模式：完全替换现有知识库
                return self.save_knowledge_base(imported_knowledge)
                
        except Exception as e:
            print(f"❌ 知识库导入失败: {e}")
            return False
    
    def validate_knowledge_structure(self, knowledge: Dict[str, Any]) -> List[str]:
        """验证知识库结构"""
        errors = []
        
        # 检查必需的顶级键
        required_keys = ["room_advice", "direction_advice", "color_advice"]
        for key in required_keys:
            if key not in knowledge:
                errors.append(f"缺少必需的键: {key}")
            elif not isinstance(knowledge[key], dict):
                errors.append(f"键 {key} 必须是字典类型")
        
        # 检查房间建议结构
        if "room_advice" in knowledge:
            for room_type, advice in knowledge["room_advice"].items():
                if not isinstance(advice, dict):
                    errors.append(f"房间建议 {room_type} 必须是字典类型")
                elif "basic" not in advice:
                    errors.append(f"房间建议 {room_type} 缺少 'basic' 字段")
        
        return errors

# 实时更新机制
class LiveKnowledgeBaseUpdater:
    """实时知识库更新器 - 不重启Agent的热更新"""
    
    def __init__(self, manager: KnowledgeBaseManager):
        self.manager = manager
        self.cache_clear_signal = manager.base_path / ".cache_clear_signal"
    
    async def monitor_updates(self):
        """监控知识库更新信号"""
        while True:
            try:
                if self.cache_clear_signal.exists():
                    # 发现更新信号，清除缓存
                    await self._notify_agent_update()
                    self.cache_clear_signal.unlink()  # 删除信号文件
                
                await asyncio.sleep(1)  # 每秒检查一次
            except Exception as e:
                print(f"⚠️ 更新监控错误: {e}")
                await asyncio.sleep(5)
    
    async def _notify_agent_update(self):
        """通知Agent更新知识库"""
        try:
            # 这里可以通过LiveKit的数据通道或其他机制通知Agent
            # 目前使用文件信号的方式
            print("🔄 检测到知识库更新，正在刷新缓存...")
            
            # 重新加载知识库到全局变量
            # 这需要在agent.py中实现相应的监控机制
            
        except Exception as e:
            print(f"❌ Agent更新通知失败: {e}")

def main():
    """命令行接口"""
    import sys

    if len(sys.argv) < 2:
        print("用法: python knowledge_base_manager.py <command> [args...]")
        print("命令:")
        print("  get_stats - 获取统计信息")
        print("  add_entry <category> <key> <data_json> - 添加条目")
        print("  update_entry <category> <key> <data_json> - 更新条目")
        print("  delete_entry <category> <key> - 删除条目")
        print("  import <file_path> <merge> - 导入知识库")
        print("  export <file_path> - 导出知识库")
        return

    manager = KnowledgeBaseManager()
    command = sys.argv[1]

    try:
        if command == "get_stats":
            stats = manager.get_statistics()
            print(json.dumps(stats, ensure_ascii=False))

        elif command == "add_entry":
            if len(sys.argv) != 5:
                print("用法: add_entry <category> <key> <data_json>")
                return

            category, key, data_json = sys.argv[2], sys.argv[3], sys.argv[4]
            data = json.loads(data_json)

            if category == "room_advice":
                success = manager.add_room_advice(key, data)
            elif category == "direction_advice":
                success = manager.add_direction_advice(key, data)
            elif category == "color_advice":
                success = manager.add_color_advice(key, data)
            else:
                print(f"未知类别: {category}")
                return

            print(json.dumps({"success": success}))

        elif command == "update_entry":
            if len(sys.argv) != 5:
                print("用法: update_entry <category> <key> <data_json>")
                return

            category, key, data_json = sys.argv[2], sys.argv[3], sys.argv[4]
            data = json.loads(data_json)

            if category == "room_advice":
                success = manager.update_room_advice(key, data)
            else:
                # 对于其他类别，先删除再添加
                knowledge = manager.load_knowledge_base()
                if category in knowledge and key in knowledge[category]:
                    knowledge[category][key].update(data)
                    success = manager.save_knowledge_base(knowledge)
                else:
                    success = False

            print(json.dumps({"success": success}))

        elif command == "delete_entry":
            if len(sys.argv) != 4:
                print("用法: delete_entry <category> <key>")
                return

            category, key = sys.argv[2], sys.argv[3]

            if category == "room_advice":
                success = manager.delete_room_advice(key)
            else:
                knowledge = manager.load_knowledge_base()
                if category in knowledge and key in knowledge[category]:
                    del knowledge[category][key]
                    success = manager.save_knowledge_base(knowledge)
                else:
                    success = False

            print(json.dumps({"success": success}))

        elif command == "import":
            if len(sys.argv) != 4:
                print("用法: import <file_path> <merge>")
                return

            file_path, merge_str = sys.argv[2], sys.argv[3]
            merge = merge_str.lower() == "true"
            success = manager.import_knowledge_base(file_path, merge)
            print(json.dumps({"success": success}))

        elif command == "export":
            if len(sys.argv) != 3:
                print("用法: export <file_path>")
                return

            file_path = sys.argv[2]
            success = manager.export_knowledge_base(file_path)
            print(json.dumps({"success": success}))

        else:
            print(f"未知命令: {command}")

    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}))

if __name__ == "__main__":
    main()
