#!/usr/bin/env python3
"""
LiveKit风水AI助手性能诊断工具
基于官方开发指南和备份文档配置进行系统性性能分析
"""

import asyncio
import time
import json
import os
import sys
from typing import Dict, List, Any
from dotenv import load_dotenv

# 添加项目路径
sys.path.append('/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent')

load_dotenv('/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/.env')

class PerformanceDiagnostics:
    """LiveKit Agent性能诊断器"""
    
    def __init__(self):
        self.results = {}
        
    async def test_network_latency(self):
        """测试LiveKit Cloud Singapore网络延迟"""
        print("🌐 测试网络连接性能...")
        
        import subprocess
        import statistics
        
        # 测试到LiveKit Cloud的延迟
        livekit_host = "kjh-a5mlk6sq.livekit.cloud"
        ping_times = []
        
        try:
            for i in range(5):
                result = subprocess.run(
                    ['ping', '-c', '1', '-W', '3000', livekit_host],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    # 解析ping时间
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'time=' in line:
                            time_str = line.split('time=')[1].split(' ')[0]
                            ping_times.append(float(time_str))
                            break
                await asyncio.sleep(0.2)
                
            if ping_times:
                avg_latency = statistics.mean(ping_times)
                min_latency = min(ping_times)
                max_latency = max(ping_times)
                
                self.results['network'] = {
                    'avg_latency_ms': round(avg_latency, 2),
                    'min_latency_ms': round(min_latency, 2),
                    'max_latency_ms': round(max_latency, 2),
                    'status': 'good' if avg_latency < 100 else 'warning' if avg_latency < 200 else 'poor'
                }
                print(f"   ✅ 平均延迟: {avg_latency:.2f}ms")
                print(f"   📊 延迟范围: {min_latency:.2f}ms - {max_latency:.2f}ms")
            else:
                self.results['network'] = {'status': 'failed', 'error': 'Unable to ping LiveKit server'}
                print("   ❌ 网络连接测试失败")
                
        except Exception as e:
            self.results['network'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ 网络测试错误: {e}")
    
    async def test_deepgram_stt_performance(self):
        """测试Deepgram STT性能"""
        print("🎤 测试Deepgram STT性能...")
        
        try:
            from livekit.plugins import deepgram
            
            # 创建STT实例
            stt = deepgram.STT(model="nova-2", language="zh-CN")
            
            # 模拟音频数据测试
            start_time = time.time()
            
            # 这里应该用实际音频数据测试，但为了诊断，我们测试初始化时间
            init_time = time.time() - start_time
            
            self.results['deepgram_stt'] = {
                'init_time_ms': round(init_time * 1000, 2),
                'status': 'initialized',
                'model': 'nova-2',
                'language': 'zh-CN'
            }
            print(f"   ✅ 初始化时间: {init_time*1000:.2f}ms")
            
        except Exception as e:
            self.results['deepgram_stt'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Deepgram STT测试失败: {e}")
    
    async def test_deepseek_llm_performance(self):
        """测试DeepSeek LLM性能"""
        print("🧠 测试DeepSeek LLM性能...")
        
        try:
            from livekit.plugins import openai
            
            # 创建DeepSeek LLM实例
            start_time = time.time()
            llm = openai.LLM.with_deepseek(
                model="deepseek-chat",
                temperature=0.7
            )
            init_time = time.time() - start_time
            
            # 测试简单推理
            test_prompt = "你好，请简单介绍一下风水。"
            inference_start = time.time()
            
            # 注意：这里只测试初始化，实际推理需要在Agent会话中进行
            inference_time = time.time() - inference_start
            
            self.results['deepseek_llm'] = {
                'init_time_ms': round(init_time * 1000, 2),
                'model': 'deepseek-chat',
                'temperature': 0.7,
                'status': 'initialized'
            }
            print(f"   ✅ 初始化时间: {init_time*1000:.2f}ms")
            
        except Exception as e:
            self.results['deepseek_llm'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ DeepSeek LLM测试失败: {e}")
    
    async def test_cartesia_tts_performance(self):
        """测试Cartesia TTS性能"""
        print("🔊 测试Cartesia TTS性能...")
        
        try:
            from livekit.plugins import cartesia
            
            # 创建TTS实例
            start_time = time.time()
            tts = cartesia.TTS(
                model="sonic-2",
                voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",
                language="zh"
            )
            init_time = time.time() - start_time
            
            self.results['cartesia_tts'] = {
                'init_time_ms': round(init_time * 1000, 2),
                'model': 'sonic-2',
                'voice_id': 'f786b574-daa5-4673-aa0c-cbe3e8534c02',
                'language': 'zh',
                'status': 'initialized'
            }
            print(f"   ✅ 初始化时间: {init_time*1000:.2f}ms")
            
        except Exception as e:
            self.results['cartesia_tts'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Cartesia TTS测试失败: {e}")
    
    async def test_knowledge_base_performance(self):
        """测试知识库工具性能"""
        print("📚 测试知识库工具性能...")
        
        try:
            # 导入知识库工具
            from agent import load_fengshui_knowledge, get_fengshui_advice, get_direction_advice, get_color_advice
            
            # 测试知识库加载性能
            start_time = time.time()
            knowledge = load_fengshui_knowledge()
            load_time = time.time() - start_time
            
            if knowledge:
                # 测试工具调用性能
                test_cases = [
                    ("客厅", "客厅"),
                    ("东方", None),
                    ("红色", None)
                ]
                
                tool_times = []
                
                for case in test_cases:
                    start_time = time.time()
                    if case[1]:  # 房间建议
                        await get_fengshui_advice(case[0], case[1])
                    elif "方" in case[0]:  # 方位建议
                        await get_direction_advice(case[0])
                    else:  # 颜色建议
                        await get_color_advice(case[0])
                    
                    call_time = time.time() - start_time
                    tool_times.append(call_time * 1000)
                
                avg_tool_time = sum(tool_times) / len(tool_times)
                
                self.results['knowledge_base'] = {
                    'load_time_ms': round(load_time * 1000, 2),
                    'avg_tool_call_ms': round(avg_tool_time, 2),
                    'room_types': len(knowledge.get('room_advice', {})),
                    'directions': len(knowledge.get('direction_advice', {})),
                    'colors': len(knowledge.get('color_advice', {})),
                    'status': 'good'
                }
                print(f"   ✅ 知识库加载: {load_time*1000:.2f}ms")
                print(f"   ✅ 平均工具调用: {avg_tool_time:.2f}ms")
                print(f"   📊 数据量: {len(knowledge.get('room_advice', {}))}房间, {len(knowledge.get('direction_advice', {}))}方位, {len(knowledge.get('color_advice', {}))}颜色")
            else:
                self.results['knowledge_base'] = {'status': 'failed', 'error': 'Knowledge base not loaded'}
                
        except Exception as e:
            self.results['knowledge_base'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ 知识库测试失败: {e}")
    
    async def run_full_diagnostics(self):
        """运行完整的性能诊断"""
        print("🚀 开始LiveKit风水AI助手性能诊断")
        print("=" * 60)
        
        # 按顺序执行所有测试
        await self.test_network_latency()
        print()
        
        await self.test_deepgram_stt_performance()
        print()
        
        await self.test_deepseek_llm_performance()
        print()
        
        await self.test_cartesia_tts_performance()
        print()
        
        await self.test_knowledge_base_performance()
        print()
        
        # 生成性能报告
        self.generate_performance_report()
    
    def generate_performance_report(self):
        """生成性能报告和优化建议"""
        print("📊 性能诊断报告")
        print("=" * 60)
        
        # 网络性能分析
        if 'network' in self.results:
            net = self.results['network']
            if net.get('status') == 'good':
                print("🟢 网络连接: 优秀")
            elif net.get('status') == 'warning':
                print("🟡 网络连接: 需要优化")
                print("   建议: 考虑使用CDN或更近的服务器")
            else:
                print("🔴 网络连接: 存在问题")
        
        # 组件性能汇总
        components = ['deepgram_stt', 'deepseek_llm', 'cartesia_tts', 'knowledge_base']
        for comp in components:
            if comp in self.results:
                result = self.results[comp]
                if result.get('status') in ['initialized', 'good']:
                    print(f"🟢 {comp}: 正常")
                else:
                    print(f"🔴 {comp}: {result.get('error', '未知错误')}")
        
        # 保存详细报告
        with open('performance_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: performance_report.json")
        
        # 生成优化建议
        self.generate_optimization_recommendations()
    
    def generate_optimization_recommendations(self):
        """生成优化建议"""
        print("\n🎯 性能优化建议")
        print("=" * 60)
        
        recommendations = []
        
        # 网络优化建议
        if 'network' in self.results:
            net = self.results['network']
            if net.get('avg_latency_ms', 0) > 100:
                recommendations.append("1. 网络延迟较高，建议检查网络连接或考虑使用更近的LiveKit服务器")
        
        # 知识库优化建议
        if 'knowledge_base' in self.results:
            kb = self.results['knowledge_base']
            if kb.get('load_time_ms', 0) > 100:
                recommendations.append("2. 知识库加载时间较长，建议实现缓存机制")
            if kb.get('avg_tool_call_ms', 0) > 50:
                recommendations.append("3. 工具调用时间较长，建议优化查询算法")
        
        # 通用优化建议
        recommendations.extend([
            "4. 启用LiveKit的预连接缓冲 (pre-connect buffer)",
            "5. 配置适当的VAD敏感度以减少误触发",
            "6. 使用流式响应减少感知延迟",
            "7. 实现智能缓存机制缓存常用查询结果"
        ])
        
        for rec in recommendations:
            print(f"   {rec}")

async def main():
    """主函数"""
    diagnostics = PerformanceDiagnostics()
    await diagnostics.run_full_diagnostics()

if __name__ == "__main__":
    asyncio.run(main())
