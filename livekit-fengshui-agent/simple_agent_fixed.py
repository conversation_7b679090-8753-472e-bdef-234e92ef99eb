"""
LiveKit风水AI助手 - 简化版本（修复连接问题）
基于备份文档的稳定配置
"""

import asyncio
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return None

# 全局加载知识库
FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(location: str, room_type: str):
    """获取风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "room_advice" in FENGSHUI_KNOWLEDGE:
        room_data = FENGSHUI_KNOWLEDGE["room_advice"].get(room_type)
        if room_data:
            advice = room_data.get("basic", "")
            detailed = room_data.get("detailed", {})
            for category, content in detailed.items():
                advice += f"\n【{category}】{content}"
        else:
            advice = f"抱歉，暂时没有关于{room_type}的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "location": location, "room_type": room_type}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "direction_advice" in FENGSHUI_KNOWLEDGE:
        direction_data = FENGSHUI_KNOWLEDGE["direction_advice"].get(direction)
        if direction_data:
            advice = f"【{direction}方位风水建议】\n"
            advice += f"五行属性：{direction_data.get('属性', '未知')}\n"
            advice += f"适合布置：{direction_data.get('适合', '无特殊建议')}\n"
            advice += f"推荐颜色：{direction_data.get('颜色', '无特殊要求')}\n"
            advice += f"注意禁忌：{direction_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{direction}方位的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "color_advice" in FENGSHUI_KNOWLEDGE:
        color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
        if color_data:
            advice = f"【{color}在风水中的寓意】\n"
            advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
            advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
            advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{color}的详细风水建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 简化版本"""
    
    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 简化版本"""
    print("🏮 启动风水AI助手...")
    
    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")
    
    # 初始化语音合成 (Cartesia)
    tts = cartesia.TTS(
        model="sonic-multilingual",
        voice="79a125e8-cd45-4c13-8a67-188112f4dd22",  # 中文女声
        language="zh",
    )
    print("✅ Cartesia TTS初始化成功")
    
    # 初始化语音活动检测
    vad = silero.VAD.load()
    print("✅ Silero VAD初始化成功")
    
    # 使用DeepSeek LLM
    from livekit.plugins import openai
    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # 简化配置，避免超时问题
        )
    )
