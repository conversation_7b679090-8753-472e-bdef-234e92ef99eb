"use client";

/**
 * LiveKit风水AI助手 - 知识库管理面板
 * 基于LiveKit官方开发指南和最佳实践
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Download, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  RefreshCw,
  Database,
  BarChart3,
  FileJson
} from 'lucide-react';

interface KnowledgeBaseStats {
  total_rooms: number;
  total_directions: number;
  total_colors: number;
  total_entries: number;
  last_updated: string;
  version: string;
  file_size_kb: number;
}

interface RoomAdvice {
  basic: string;
  detailed: Record<string, string>;
}

interface DirectionAdvice {
  属性: string;
  适合: string;
  颜色: string;
  禁忌: string;
}

interface ColorAdvice {
  寓意: string;
  适用: string;
  禁忌: string;
}

export default function KnowledgeBaseAdmin() {
  const [stats, setStats] = useState<KnowledgeBaseStats | null>(null);
  const [roomAdvice, setRoomAdvice] = useState<Record<string, RoomAdvice>>({});
  const [directionAdvice, setDirectionAdvice] = useState<Record<string, DirectionAdvice>>({});
  const [colorAdvice, setColorAdvice] = useState<Record<string, ColorAdvice>>({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  // 编辑状态
  const [editingRoom, setEditingRoom] = useState<string | null>(null);
  const [editingDirection, setEditingDirection] = useState<string | null>(null);
  const [editingColor, setEditingColor] = useState<string | null>(null);
  
  // 新增表单状态
  const [newRoomForm, setNewRoomForm] = useState({ name: '', basic: '', detailed: {} });
  const [newDirectionForm, setNewDirectionForm] = useState({ name: '', 属性: '', 适合: '', 颜色: '', 禁忌: '' });
  const [newColorForm, setNewColorForm] = useState({ name: '', 寓意: '', 适用: '', 禁忌: '' });

  useEffect(() => {
    loadKnowledgeBase();
  }, []);

  const loadKnowledgeBase = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // 目前使用模拟数据
      const mockStats: KnowledgeBaseStats = {
        total_rooms: 6,
        total_directions: 8,
        total_colors: 6,
        total_entries: 20,
        last_updated: new Date().toISOString(),
        version: "1.0",
        file_size_kb: 15.2
      };
      
      setStats(mockStats);
      
      // 加载实际数据的API调用应该在这里
      // const response = await fetch('/api/knowledge-base');
      // const data = await response.json();
      
    } catch (error) {
      setMessage({ type: 'error', text: '加载知识库失败' });
    } finally {
      setLoading(false);
    }
  };

  const saveEntry = async (category: string, key: string, data: any) => {
    setLoading(true);
    try {
      // 这里应该调用实际的保存API
      // await fetch(`/api/knowledge-base/${category}/${key}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data)
      // });
      
      setMessage({ type: 'success', text: '保存成功' });
      
      // 清除编辑状态
      setEditingRoom(null);
      setEditingDirection(null);
      setEditingColor(null);
      
    } catch (error) {
      setMessage({ type: 'error', text: '保存失败' });
    } finally {
      setLoading(false);
    }
  };

  const deleteEntry = async (category: string, key: string) => {
    if (!confirm(`确定要删除 ${key} 吗？`)) return;
    
    setLoading(true);
    try {
      // 这里应该调用实际的删除API
      // await fetch(`/api/knowledge-base/${category}/${key}`, {
      //   method: 'DELETE'
      // });
      
      setMessage({ type: 'success', text: '删除成功' });
      await loadKnowledgeBase();
      
    } catch (error) {
      setMessage({ type: 'error', text: '删除失败' });
    } finally {
      setLoading(false);
    }
  };

  const exportKnowledgeBase = async () => {
    try {
      // 这里应该调用导出API
      // const response = await fetch('/api/knowledge-base/export');
      // const blob = await response.blob();
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = `fengshui_knowledge_${new Date().toISOString().split('T')[0]}.json`;
      // a.click();
      
      setMessage({ type: 'success', text: '导出成功' });
    } catch (error) {
      setMessage({ type: 'error', text: '导出失败' });
    }
  };

  const importKnowledgeBase = async (file: File, merge: boolean = false) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('merge', merge.toString());
      
      // 这里应该调用导入API
      // await fetch('/api/knowledge-base/import', {
      //   method: 'POST',
      //   body: formData
      // });
      
      setMessage({ type: 'success', text: '导入成功' });
      await loadKnowledgeBase();
      
    } catch (error) {
      setMessage({ type: 'error', text: '导入失败' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Database className="h-8 w-8" />
          风水知识库管理
        </h1>
        <div className="flex gap-2">
          <Button onClick={loadKnowledgeBase} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button onClick={exportKnowledgeBase} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" onClick={() => document.getElementById('import-file')?.click()}>
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
          <input
            id="import-file"
            type="file"
            accept=".json"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) importKnowledgeBase(file);
            }}
          />
        </div>
      </div>

      {message && (
        <Alert className={message.type === 'error' ? 'border-red-500' : 'border-green-500'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总条目</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_entries}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">房间建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_rooms}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">方位建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_directions}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">颜色建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_colors}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 知识库内容管理 */}
      <Tabs defaultValue="rooms" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rooms">房间建议</TabsTrigger>
          <TabsTrigger value="directions">方位建议</TabsTrigger>
          <TabsTrigger value="colors">颜色建议</TabsTrigger>
          <TabsTrigger value="settings">设置</TabsTrigger>
        </TabsList>

        <TabsContent value="rooms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                房间风水建议管理
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  添加房间
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                房间建议管理功能正在开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="directions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                方位风水建议管理
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  添加方位
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                方位建议管理功能正在开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                颜色风水建议管理
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  添加颜色
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                颜色建议管理功能正在开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">知识库版本</label>
                  <Input value={stats?.version || ''} disabled />
                </div>
                <div>
                  <label className="text-sm font-medium">文件大小</label>
                  <Input value={`${stats?.file_size_kb || 0} KB`} disabled />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">最后更新时间</label>
                <Input 
                  value={stats?.last_updated ? new Date(stats.last_updated).toLocaleString('zh-CN') : ''} 
                  disabled 
                />
              </div>
              
              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-2">实时更新设置</h3>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    ✅ 热更新已启用
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    知识库更新后无需重启Agent
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
