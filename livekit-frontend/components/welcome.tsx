import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

interface WelcomeProps {
  disabled: boolean;
  startButtonText: string;
  onStartCall: () => void;
}

export const Welcome = ({
  disabled,
  startButtonText,
  onStartCall,
  ref,
}: React.ComponentProps<'div'> & WelcomeProps) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div
      ref={ref}
      inert={disabled}
      className="fixed inset-0 z-10 mx-auto flex h-svh flex-col items-center justify-center text-center"
    >
      {/* 动态旋转八卦图标 - 使用用户上传的T2.png */}
      {isClient ? (
        <div className="mb-4 flex size-20 items-center justify-center">
          <img
            src="/bagua-square.png"
            alt="动态旋转八卦图"
            className="animate-spin-slow h-20 w-20"
            style={{
              filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))',
              animationDuration: '12s',
              animationIterationCount: 'infinite',
              animationTimingFunction: 'linear',
            }}
          />
        </div>
      ) : (
        <div className="mb-4 flex size-20 items-center justify-center">
          <div className="h-20 w-20 animate-pulse rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600" />
        </div>
      )}

      <p className="text-fg1 max-w-prose pt-1 leading-6 font-medium">
        🏮 与专业风水大师AI实时语音咨询
      </p>
      <p className="text-fg2 max-w-prose pt-2 text-sm leading-5">
        • 房屋布局风水分析 • 家具摆放建议 • 颜色搭配指导 • 风水改善方案
      </p>
      <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-900/20">
        <p className="text-xs leading-4 text-blue-700 dark:text-blue-300">
          💡 <strong>智能知识库已激活</strong>
          ：AI助手可自动调用专业风水知识库，为您提供房间布局、方位选择、颜色搭配等专业建议。直接语音提问即可获得详细解答。
        </p>
      </div>
      <Button variant="primary" size="lg" onClick={onStartCall} className="mt-6 w-64 font-mono">
        {startButtonText}
      </Button>
      <p className="text-fg1 m fixed bottom-5 left-1/2 w-full max-w-prose -translate-x-1/2 pt-1 text-xs leading-5 font-normal text-pretty md:text-sm">
        基于归云.中国和DeepSeek技术构建的专业风水知识库 |{' '}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://docs.livekit.io/agents/start/voice-ai/"
          className="underline"
        >
          技术文档
        </a>
      </p>
    </div>
  );
};
