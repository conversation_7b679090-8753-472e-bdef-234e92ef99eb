/**
 * LiveKit风水AI助手 - 知识库导入API
 */

import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

const AGENT_PATH = '/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent';

// 验证知识库结构
function validateKnowledgeStructure(knowledge: any): string[] {
  const errors: string[] = [];
  
  // 检查必需的顶级键
  const requiredKeys = ['room_advice', 'direction_advice', 'color_advice'];
  for (const key of requiredKeys) {
    if (!(key in knowledge)) {
      errors.push(`缺少必需的键: ${key}`);
    } else if (typeof knowledge[key] !== 'object' || knowledge[key] === null) {
      errors.push(`键 ${key} 必须是对象类型`);
    }
  }
  
  // 检查房间建议结构
  if ('room_advice' in knowledge && typeof knowledge.room_advice === 'object') {
    for (const [roomType, advice] of Object.entries(knowledge.room_advice)) {
      if (typeof advice !== 'object' || advice === null) {
        errors.push(`房间建议 ${roomType} 必须是对象类型`);
      } else if (!('basic' in (advice as any))) {
        errors.push(`房间建议 ${roomType} 缺少 'basic' 字段`);
      }
    }
  }
  
  return errors;
}

// 执行Python导入命令
async function executePythonImport(filePath: string, merge: boolean): Promise<any> {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn('python3', [
      path.join('/www/wwwroot/su.guiyunai.fun', 'knowledge_base_manager.py'),
      'import',
      filePath,
      merge.toString()
    ], {
      cwd: AGENT_PATH,
      env: { ...process.env, PYTHONPATH: AGENT_PATH }
    });

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(stdout);
          resolve(result);
        } catch (e) {
          resolve({ success: true, output: stdout });
        }
      } else {
        reject(new Error(stderr || `Process exited with code ${code}`));
      }
    });
  });
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const merge = formData.get('merge') === 'true';
    
    if (!file) {
      return NextResponse.json(
        { error: '请选择要导入的文件' },
        { status: 400 }
      );
    }
    
    // 检查文件类型
    if (!file.name.endsWith('.json')) {
      return NextResponse.json(
        { error: '只支持JSON格式的文件' },
        { status: 400 }
      );
    }
    
    // 读取文件内容
    const fileContent = await file.text();
    let knowledge;
    
    try {
      knowledge = JSON.parse(fileContent);
    } catch (error) {
      return NextResponse.json(
        { error: '文件格式错误，请确保是有效的JSON文件' },
        { status: 400 }
      );
    }
    
    // 验证知识库结构
    const validationErrors = validateKnowledgeStructure(knowledge);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          error: '知识库结构验证失败',
          details: validationErrors
        },
        { status: 400 }
      );
    }
    
    // 保存临时文件
    const tempDir = path.join(AGENT_PATH, 'temp');
    await fs.mkdir(tempDir, { recursive: true });
    
    const tempFilePath = path.join(tempDir, `import_${Date.now()}.json`);
    await fs.writeFile(tempFilePath, fileContent, 'utf-8');
    
    try {
      // 执行导入
      const result = await executePythonImport(tempFilePath, merge);
      
      // 清理临时文件
      await fs.unlink(tempFilePath);
      
      return NextResponse.json({
        success: true,
        message: merge ? '知识库合并成功' : '知识库导入成功',
        result
      });
      
    } catch (importError) {
      // 清理临时文件
      try {
        await fs.unlink(tempFilePath);
      } catch (cleanupError) {
        console.error('Failed to cleanup temp file:', cleanupError);
      }
      
      throw importError;
    }
    
  } catch (error) {
    console.error('Knowledge base import error:', error);
    return NextResponse.json(
      { 
        error: '导入失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
