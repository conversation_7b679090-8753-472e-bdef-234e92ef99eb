/**
 * LiveKit风水AI助手 - 知识库导出API
 */

import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const AGENT_PATH = '/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent';
const KNOWLEDGE_FILE = path.join(AGENT_PATH, 'fengshui_knowledge.json');

export async function GET(request: NextRequest) {
  try {
    // 读取知识库文件
    const content = await fs.readFile(KNOWLEDGE_FILE, 'utf-8');
    const knowledge = JSON.parse(content);
    
    // 添加导出元数据
    const exportData = {
      ...knowledge,
      export_info: {
        exported_at: new Date().toISOString(),
        exported_by: 'LiveKit风水AI助手管理系统',
        version: knowledge.version || '1.0'
      }
    };
    
    // 生成文件名
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `fengshui_knowledge_${timestamp}.json`;
    
    // 返回JSON文件
    return new NextResponse(JSON.stringify(exportData, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Knowledge base export error:', error);
    return NextResponse.json(
      { error: 'Failed to export knowledge base' },
      { status: 500 }
    );
  }
}
