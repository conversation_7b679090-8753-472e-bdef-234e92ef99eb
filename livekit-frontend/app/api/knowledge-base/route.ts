/**
 * LiveKit风水AI助手 - 知识库管理API
 * 基于LiveKit官方开发指南和最佳实践
 */

import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

const AGENT_PATH = '/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent';
const KNOWLEDGE_FILE = path.join(AGENT_PATH, 'fengshui_knowledge.json');

// 执行Python知识库管理器命令
async function executePythonCommand(command: string, args: string[] = []): Promise<any> {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn('python3', [
      path.join('/www/wwwroot/su.guiyunai.fun', 'knowledge_base_manager.py'),
      command,
      ...args
    ], {
      cwd: AGENT_PATH,
      env: { ...process.env, PYTHONPATH: AGENT_PATH }
    });

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(stdout);
          resolve(result);
        } catch (e) {
          resolve({ success: true, output: stdout });
        }
      } else {
        reject(new Error(stderr || `Process exited with code ${code}`));
      }
    });
  });
}

// GET - 获取知识库统计信息和内容
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type === 'stats') {
      // 获取统计信息
      const stats = await executePythonCommand('get_stats');
      return NextResponse.json(stats);
    } else if (type === 'content') {
      // 获取完整内容
      const content = await fs.readFile(KNOWLEDGE_FILE, 'utf-8');
      const knowledge = JSON.parse(content);
      return NextResponse.json(knowledge);
    } else {
      // 默认返回统计信息
      const stats = await executePythonCommand('get_stats');
      return NextResponse.json(stats);
    }
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch knowledge base data' },
      { status: 500 }
    );
  }
}

// POST - 添加新条目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category, key, data } = body;

    if (!category || !key || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: category, key, data' },
        { status: 400 }
      );
    }

    const result = await executePythonCommand('add_entry', [
      category,
      key,
      JSON.stringify(data)
    ]);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Knowledge base add error:', error);
    return NextResponse.json(
      { error: 'Failed to add knowledge base entry' },
      { status: 500 }
    );
  }
}

// PUT - 更新现有条目
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { category, key, data } = body;

    if (!category || !key || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: category, key, data' },
        { status: 400 }
      );
    }

    const result = await executePythonCommand('update_entry', [
      category,
      key,
      JSON.stringify(data)
    ]);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Knowledge base update error:', error);
    return NextResponse.json(
      { error: 'Failed to update knowledge base entry' },
      { status: 500 }
    );
  }
}

// DELETE - 删除条目
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const key = searchParams.get('key');

    if (!category || !key) {
      return NextResponse.json(
        { error: 'Missing required parameters: category, key' },
        { status: 400 }
      );
    }

    const result = await executePythonCommand('delete_entry', [category, key]);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Knowledge base delete error:', error);
    return NextResponse.json(
      { error: 'Failed to delete knowledge base entry' },
      { status: 500 }
    );
  }
}
