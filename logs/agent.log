{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-03T06:16:12.189272+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "livekit.plugins.turn_detector", "av"], "timestamp": "2025-08-03T06:16:12.189672+00:00"}
{"message": "starting inference executor", "level": "INFO", "name": "livekit.agents", "timestamp": "2025-08-03T06:16:12.200837+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 104315, "inference": true, "timestamp": "2025-08-03T06:16:13.983539+00:00"}
None of PyTorch, TensorFlow >= 2.0, or Flax have been found. Models won't be available and only tokenizers, configuration and file/data utilities can be used.
{"message": "killing process", "level": "INFO", "name": "livekit.agents", "pid": 104315, "inference": true, "timestamp": "2025-08-03T06:16:23.994754+00:00"}
{"message": "worker failed", "level": "ERROR", "name": "livekit.agents", "exc_info": "Traceback (most recent call last):\n  File \"/usr/lib/python3.12/asyncio/tasks.py\", line 520, in wait_for\n    return await fut\n           ^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/ipc/channel.py\", line 47, in arecv_message\n    return _read_message(await dplx.recv_bytes(), messages)\n                         ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/utils/aio/duplex_unix.py\", line 35, in recv_bytes\n    len_bytes = await self._reader.readexactly(4)\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/streams.py\", line 752, in readexactly\n    await self._wait_for_data('readexactly')\n  File \"/usr/lib/python3.12/asyncio/streams.py\", line 545, in _wait_for_data\n    await self._waiter\nasyncio.exceptions.CancelledError\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/_run.py\", line 79, in _worker_run\n    await worker.run()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/worker.py\", line 387, in run\n    await self._inference_executor.initialize()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/ipc/supervised_proc.py\", line 169, in initialize\n    init_res = await asyncio.wait_for(\n               ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/tasks.py\", line 519, in wait_for\n    async with timeouts.timeout(timeout):\n  File \"/usr/lib/python3.12/asyncio/timeouts.py\", line 115, in __aexit__\n    raise TimeoutError from exc_val\nTimeoutError", "timestamp": "2025-08-03T06:16:23.995340+00:00"}
{"message": "draining worker", "level": "INFO", "name": "livekit.agents", "id": "unregistered", "timeout": 1800, "timestamp": "2025-08-03T06:16:24.087354+00:00"}
